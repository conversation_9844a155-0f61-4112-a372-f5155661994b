{"buildCommand": "expo export -p web", "outputDirectory": "dist", "devCommand": "expo", "cleanUrls": true, "framework": null, "rewrites": [{"source": "/:path*", "destination": "/"}], "headers": [{"source": "/rive/(.*).riv", "headers": [{"key": "Content-Type", "value": "application/octet-stream"}]}, {"source": "/(.*)", "headers": [{"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' https://widgets.in.webengage.com 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https:; font-src 'self' https://fonts.gstatic.com data:; connect-src 'self' https://matiks.com https://server.matiks.com https://dev.matiks.com https://dev.server.matiks.com https://widgets.in.webengage.com https://api.mixpanel.com wss://matiks.com wss://server.matiks.com wss://dev.matiks.com wss://dev.server.matiks.com ws://localhost:* wss://localhost:* http://localhost:* https://localhost:* http://localhost:4000 https://localhost:4000; frame-ancestors 'self'; object-src 'none'; base-uri 'self'; form-action 'self'"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}]}]}