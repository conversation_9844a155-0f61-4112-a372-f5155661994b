{"name": "matiks", "version": "1.0.0", "main": "expo-router/entry", "packageManager": "yarn@1.22.19", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "start": "expo start", "start-local:android": "npx expo start --localhost --android", "start-local:ios": "npx expo start --localhost --ios", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "build:web": "NODE_ENV=production expo export -p web", "build-android:prod": "eas build --profile production --platform android", "build-android:preview": "eas build --profile preview --platform android", "build-android:local": "eas build --profile development --platform android --local", "build-ios:prod": "eas build --profile production --platform ios", "update:prod": "eas update --channel production --clear-cache --message \"Adding Practice modules\" --platform android", "submit:ios": "eas submit -p ios", "submit:android": "eas submit -p android", "build-ios:local": "eas build --profile development --platform ios --local", "lint-all": "eslint src app --ext .js,.jsx,.ts,.tsx", "lint": "npm run lint-all -- --fix", "validate": "lint-staged --config .lintstagedrc.json", "postinstall": "node ./__quickfix/quickfix.js", "eas-build-on-success": "npx bugsnag-eas-build-on-success"}, "dependencies": {"@apollo/client": "^3.10.4", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@bugsnag/expo": "^51.0.0", "@charmy.tech/react-native-stroke-text": "^1.2.3", "@expo/match-media": "^0.4.0", "@expo/metro-runtime": "~3.2.1", "@expo/vector-icons": "^14.0.3", "@lottiefiles/dotlottie-react": "^0.9.3", "@react-native-async-storage/async-storage": "^2.0.0", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/netinfo": "11.3.1", "@react-native-firebase/analytics": "^21.6.1", "@react-native-firebase/app": "^21.6.1", "@react-native-firebase/crashlytics": "^21.6.1", "@react-native-firebase/messaging": "^21.6.1", "@react-native-google-signin/google-signin": "^12.2.1", "@react-native-masked-view/masked-view": "^0.3.1", "@react-native-picker/picker": "^2.11.0", "@react-navigation/native-stack": "^6.11.0", "@react-oauth/google": "^0.12.1", "@rive-app/react-canvas": "^4.16.5", "@rneui/base": "^4.0.0-rc.8", "@rneui/themed": "^4.0.0-rc.8", "@shopify/flash-list": "^1.7.2", "@tamagui/babel-plugin": "^1.121.3", "@tamagui/config": "^1.121.3", "@tamagui/metro-plugin": "^1.121.3", "apollo-link": "^1.2.14", "apollo-upload-client": "17.0.0", "bson": "^4.7.2", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "events": "^3.3.0", "expo": "~51.0.14", "expo-apple-authentication": "~6.4.2", "expo-application": "~5.9.1", "expo-asset": "~10.0.10", "expo-av": "~14.0.7", "expo-blur": "~13.0.2", "expo-clipboard": "~6.0.3", "expo-constants": "~16.0.2", "expo-crypto": "~13.0.2", "expo-dev-client": "~4.0.18", "expo-device": "~6.0.2", "expo-file-system": "~17.0.1", "expo-haptics": "~13.0.1", "expo-image": "~1.12.12", "expo-image-manipulator": "~12.0.5", "expo-image-picker": "~15.0.5", "expo-linking": "~6.3.1", "expo-localization": "~15.0.3", "expo-media-library": "~16.0.5", "expo-notifications": "~0.28.9", "expo-router": "~3.5.16", "expo-secure-store": "~13.0.2", "expo-sharing": "~12.0.1", "expo-status-bar": "~1.12.1", "expo-store-review": "~7.0.2", "expo-updates": "~0.25.24", "graphql": "^16.8.1", "graphql-ws": "^5.16.0", "hoist-non-react-statics": "^3.3.2", "html2canvas": "^1.4.1", "immer": "^10.1.1", "js-cookie": "^3.0.5", "katex": "^0.16.19", "lodash": "^4.17.21", "lottie-react-native": "^7.1.0", "mime": "^4.0.4", "mixpanel-react-native": "^3.0.5", "modal-enhanced-react-native-web": "^0.2.0", "numeral": "^2.0.6", "opentype.js": "^1.3.4", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.53.2", "react-native": "0.74.2", "react-native-animatable": "^1.4.0", "react-native-chart-kit": "^6.12.0", "react-native-device-info": "^14.0.0", "react-native-element-dropdown": "^2.12.2", "react-native-gesture-handler": "^2.20.0", "react-native-linear-gradient": "^2.8.3", "react-native-masked-view": "^0.2.0", "react-native-modal": "^13.0.1", "react-native-pager-view": "^6.4.1", "react-native-range-slider-expo": "^1.4.3", "react-native-reanimated": "~3.16.0", "react-native-reanimated-carousel": "4.0.0-canary.17", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "4.10.1", "react-native-screens": "3.31.1", "react-native-share": "^12.0.3", "react-native-sticky-parallax-header": "^1.1.1", "react-native-svg": "15.11.2", "react-native-tab-view": "^3.5.2", "react-native-uuid": "^2.0.2", "react-native-view-shot": "3.8.0", "react-native-web": "~0.19.6", "react-native-webengage": "^1.5.2", "react-native-webview": "^13.12.3", "react-responsive": "^10.0.0", "react-responsive-carousel": "^3.2.23", "react-text-gradients": "^1.0.2", "react-virtuoso": "^4.6.0", "rive-react-native": "^8.1.0", "sp-react-native-in-app-updates": "^1.4.0", "tamagui": "^1.121.3", "yarn": "^1.22.22", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/preset-typescript": "^7.27.0", "@bugsnag/plugin-expo-eas-sourcemaps": "^51.0.0", "@bugsnag/source-maps": "^2.3.3", "@playwright/test": "^1.52.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@tsconfig/react-native": "^3.0.3", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.15", "@types/node": "^22.15.2", "@types/opentype.js": "^1.3.8", "@types/react": "~18.2.79", "@types/react-test-renderer": "^18.0.7", "@welldone-software/why-did-you-render": "^10.0.1", "eslint": "^8.2.0", "eslint-config-airbnb": "19.0.4", "eslint-config-expo": "^7.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-perfectionist": "^2.11.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.28.0", "eslint-plugin-react-hooks": "^4.3.0", "jest": "^29.7.0", "jest-react-native": "^18.0.0", "lint-staged": "^15.2.7", "metro-minify-terser": "^0.82.2", "prettier": "^3.2.5", "prop-types": "^15.8.1", "react-test-renderer": "18.2.0", "terser": "^5.39.0", "typescript": "~5.3.3"}, "private": true}