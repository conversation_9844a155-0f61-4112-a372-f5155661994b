# Content Security Policy (CSP) Implementation Guide

## Overview

This document explains the Content Security Policy (CSP) implementation in the Matiks application. CSP is a security standard that helps prevent Cross-Site Scripting (XSS) attacks, data injection attacks, and other code injection vulnerabilities.

## What is Content Security Policy?

Content Security Policy (CSP) is a security layer that helps detect and mitigate certain types of attacks, including:

- **Cross-Site Scripting (XSS)** attacks
- **Data injection** attacks
- **Code injection** vulnerabilities
- **Clickjacking** attacks

### How CSP Works

1. **Whitelist Approach**: Instead of trying to block malicious content, CSP uses a whitelist approach where you explicitly define what sources are trusted
2. **Directive-Based**: CSP uses directives to control different types of resources (scripts, styles, images, etc.)
3. **Browser Enforcement**: Modern browsers enforce CSP policies and block any content that violates the policy
4. **Violation Reporting**: Browsers can send violation reports when CSP policies are violated

## Implementation Details

### 1. CSP Configuration (`src/core/security/csp.js`)

The main CSP configuration defines what sources are allowed for different types of content:

```javascript
export const CSP_CONFIG = {
  'script-src': [
    "'self'",
    'https://widgets.in.webengage.com',
    "'nonce-{NONCE}'"
  ],
  'style-src': [
    "'self'",
    "'nonce-{NONCE}'",
    'https://fonts.googleapis.com',
    "'unsafe-inline'"
  ],
  // ... other directives
};
```

### 2. Key Security Features

#### Inline Script Blocking
- **Before**: Inline scripts were allowed with `'unsafe-inline'`
- **After**: Inline scripts are blocked unless they have a valid nonce
- **Benefit**: Prevents XSS attacks through script injection

#### Nonce-Based Security
- Each request generates a unique cryptographic nonce
- Only scripts/styles with the correct nonce are executed
- Nonces are unpredictable and change on each page load

#### External Resource Control
- Only whitelisted external domains can load scripts
- Prevents malicious scripts from unknown sources
- Allows legitimate third-party services (WebEngage, Google Fonts)

### 3. File Structure

```
src/core/security/
├── csp.js                 # Main CSP configuration
├── cspMiddleware.js       # Express/server middleware
├── cspTester.js          # Testing utilities
└── secureHtmlTemplate.jsx # Secure HTML template

public/
├── js/webengage-init.js  # External WebEngage script
└── api/csp-report.js     # Violation reporting endpoint

docs/
└── CSP_IMPLEMENTATION.md # This documentation
```

## Security Improvements

### Before CSP Implementation

```html
<!-- VULNERABLE: Inline script without protection -->
<script>
  var webengage;
  // ... inline script code
  webengage.init('license-key');
</script>
```

### After CSP Implementation

```html
<!-- SECURE: External script with CSP protection -->
<script src="/js/webengage-init.js" async></script>

<!-- SECURE: Inline script with nonce -->
<script nonce="abc123xyz">
  // Only executes if nonce matches CSP policy
</script>
```

## CSP Directives Explained

### `script-src`
Controls which scripts can be executed:
- `'self'`: Scripts from the same origin
- `https://widgets.in.webengage.com`: WebEngage scripts
- `'nonce-{NONCE}'`: Scripts with valid nonce

### `style-src`
Controls which stylesheets can be applied:
- `'self'`: Styles from the same origin
- `'unsafe-inline'`: Inline styles (less risky than scripts)
- `https://fonts.googleapis.com`: Google Fonts

### `img-src`
Controls which images can be loaded:
- `'self'`: Images from the same origin
- `data:`: Data URLs for inline images
- `https:`: Any HTTPS image source

### `connect-src`
Controls which URLs can be contacted via fetch, WebSocket, etc.:
- `'self'`: Same origin requests
- `https://matiks.com`: API endpoints
- `wss://matiks.com`: WebSocket connections

### `frame-ancestors`
Controls which sites can embed this page in frames:
- `'self'`: Only same origin can frame

### `object-src`
Controls plugins like Flash:
- `'none'`: No plugins allowed

## Testing CSP Implementation

### 1. Automated Testing

```javascript
import { runCSPTests } from 'src/core/security/cspTester.js';

// Run comprehensive CSP tests
const results = runCSPTests();
console.log('CSP Test Results:', results);
```

### 2. Manual Testing

1. Open browser developer tools
2. Try executing inline scripts in console
3. Check for CSP violation errors
4. Verify external resources load correctly

### 3. Violation Monitoring

```javascript
import { startCSPMonitoring } from 'src/core/security/cspTester.js';

// Start real-time violation monitoring
const stopMonitoring = startCSPMonitoring();

// Stop monitoring when done
stopMonitoring();
```

## React Integration

### Using CSP Hook

```javascript
import { useCSP } from 'src/core/hooks/useCSP.js';

function MyComponent() {
  const { isCSPEnabled, violations, getNonce } = useCSP();
  
  const handleSecureAction = () => {
    const nonce = getNonce();
    // Use nonce for secure operations
  };
  
  return (
    <div>
      <p>CSP Status: {isCSPEnabled ? 'Enabled' : 'Disabled'}</p>
      <p>Violations: {violations.length}</p>
    </div>
  );
}
```

### Nonce Usage

```javascript
import { useCSPNonce } from 'src/core/hooks/useCSP.js';

function SecureComponent() {
  const nonce = useCSPNonce();
  
  return (
    <style nonce={nonce}>
      {/* Secure inline styles */}
    </style>
  );
}
```

## Deployment Configuration

### Vercel Headers

The `vercel.json` file includes CSP headers:

```json
{
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "Content-Security-Policy",
          "value": "default-src 'self'; script-src 'self' https://widgets.in.webengage.com 'nonce-{NONCE}'; ..."
        }
      ]
    }
  ]
}
```

### HTML Meta Tags

CSP is also enforced via meta tags:

```html
<meta http-equiv="Content-Security-Policy" content="...">
```

## Violation Reporting

### Automatic Reporting

CSP violations are automatically reported to `/api/csp-report` endpoint:

```javascript
// Browser automatically sends violation reports
document.addEventListener('securitypolicyviolation', (event) => {
  // Violation details sent to server
});
```

### Integration with Monitoring

Violations are integrated with existing monitoring tools:

```javascript
// Send to Bugsnag
if (global.Bugsnag) {
  global.Bugsnag.notify(new Error('CSP Violation'), {
    severity: 'warning',
    metaData: { csp: violationData }
  });
}
```

## Development vs Production

### Development Mode
- More permissive CSP for hot reloading
- `'unsafe-inline'` and `'unsafe-eval'` allowed
- Detailed violation logging

### Production Mode
- Strict CSP enforcement
- No unsafe directives
- Violation reporting to monitoring services

## Common Issues and Solutions

### Issue 1: Inline Scripts Blocked

**Problem**: Existing inline scripts stop working

**Solution**: 
1. Move scripts to external files
2. Use nonces for necessary inline scripts
3. Refactor to use event listeners instead of inline handlers

### Issue 2: Third-Party Scripts Blocked

**Problem**: External scripts from CDNs are blocked

**Solution**:
1. Add trusted domains to `script-src`
2. Verify HTTPS usage
3. Use SRI (Subresource Integrity) for additional security

### Issue 3: Dynamic Content Issues

**Problem**: Dynamically generated content violates CSP

**Solution**:
1. Use nonces for dynamic scripts
2. Whitelist specific patterns
3. Use CSP-compatible libraries

## Best Practices

### 1. Start with Report-Only Mode

```html
<meta http-equiv="Content-Security-Policy-Report-Only" content="...">
```

### 2. Use Nonces for Inline Content

```javascript
const nonce = generateNonce();
// Use nonce in templates
```

### 3. Regular Security Audits

```javascript
// Run CSP tests regularly
const auditResults = generateCSPReport();
```

### 4. Monitor Violations

- Set up alerts for CSP violations
- Review violation reports regularly
- Update CSP policy based on legitimate violations

## Migration Guide

### Step 1: Assessment
1. Run CSP tests on current application
2. Identify inline scripts and styles
3. List external resources

### Step 2: Preparation
1. Move inline scripts to external files
2. Add nonce support to templates
3. Update build process

### Step 3: Implementation
1. Add CSP headers/meta tags
2. Test in report-only mode
3. Fix violations

### Step 4: Enforcement
1. Switch to enforcement mode
2. Monitor violations
3. Iterate and improve

## Security Benefits

### XSS Prevention
- Blocks malicious script injection
- Prevents data exfiltration
- Reduces attack surface

### Data Protection
- Controls where data can be sent
- Prevents unauthorized requests
- Protects user privacy

### Compliance
- Meets security standards
- Supports regulatory requirements
- Demonstrates security commitment

## Performance Impact

### Minimal Overhead
- CSP checking is fast
- No significant performance impact
- Browser-native implementation

### Potential Benefits
- Blocks malicious resources
- Reduces bandwidth usage
- Improves user experience

## Conclusion

The CSP implementation in Matiks provides robust protection against XSS and injection attacks while maintaining application functionality. The nonce-based approach ensures security without breaking legitimate functionality.

Regular monitoring and testing ensure the CSP policy remains effective and up-to-date with application changes.

For questions or issues, refer to the CSP testing utilities or check the violation reports for guidance on policy adjustments.
