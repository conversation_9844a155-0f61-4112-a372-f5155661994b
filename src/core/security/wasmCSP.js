/**
 * WebAssembly-Specific CSP Configuration
 * 
 * This module provides utilities for managing CSP in WebAssembly contexts.
 * WebAssembly requires special CSP considerations due to its compilation model.
 */

/**
 * Check if WebAssembly is supported and CSP-compatible
 * @returns {Object} WebAssembly support information
 */
export const checkWebAssemblySupport = () => {
  const support = {
    isSupported: false,
    hasStreaming: false,
    cspCompatible: false,
    errors: []
  };

  // Check basic WebAssembly support
  if (typeof WebAssembly === 'object') {
    support.isSupported = true;

    // Check streaming support
    if (typeof WebAssembly.instantiateStreaming === 'function') {
      support.hasStreaming = true;
    }

    // Check if CSP allows WebAssembly compilation
    try {
      // Try to create a minimal WASM module to test CSP
      const wasmCode = new Uint8Array([
        0x00, 0x61, 0x73, 0x6d, // WASM magic number
        0x01, 0x00, 0x00, 0x00  // WASM version
      ]);
      
      WebAssembly.compile(wasmCode).then(() => {
        support.cspCompatible = true;
      }).catch((error) => {
        support.errors.push(`CSP blocks WASM compilation: ${error.message}`);
      });
    } catch (error) {
      support.errors.push(`WASM compilation test failed: ${error.message}`);
    }
  } else {
    support.errors.push('WebAssembly is not supported in this environment');
  }

  return support;
};

/**
 * WebAssembly-specific CSP directives
 */
export const WASM_CSP_REQUIREMENTS = {
  'script-src': [
    "'unsafe-eval'" // Required for WASM compilation
  ],
  'worker-src': [
    "'self'", // For WASM workers
    'blob:' // For worker blobs
  ]
};

/**
 * Generate CSP policy that supports WebAssembly
 * @param {Object} basePolicy - Base CSP policy
 * @returns {Object} Enhanced policy with WASM support
 */
export const enhanceCSPForWebAssembly = (basePolicy) => {
  const enhancedPolicy = { ...basePolicy };

  // Add unsafe-eval to script-src if not present
  if (enhancedPolicy['script-src']) {
    if (!enhancedPolicy['script-src'].includes("'unsafe-eval'")) {
      enhancedPolicy['script-src'] = [...enhancedPolicy['script-src'], "'unsafe-eval'"];
    }
  } else {
    enhancedPolicy['script-src'] = ["'self'", "'unsafe-eval'"];
  }

  // Add worker-src for WASM workers
  if (!enhancedPolicy['worker-src']) {
    enhancedPolicy['worker-src'] = ["'self'", 'blob:'];
  }

  return enhancedPolicy;
};

/**
 * Test WebAssembly functionality with current CSP
 * @returns {Promise<Object>} Test results
 */
export const testWebAssemblyWithCSP = async () => {
  const results = {
    timestamp: new Date().toISOString(),
    tests: [],
    summary: {
      passed: 0,
      failed: 0,
      total: 0
    }
  };

  const addTest = (name, passed, details) => {
    results.tests.push({ name, passed, details });
    if (passed) results.summary.passed++;
    else results.summary.failed++;
    results.summary.total++;
  };

  // Test 1: Basic WebAssembly support
  const basicSupport = typeof WebAssembly === 'object';
  addTest('WebAssembly Support', basicSupport, 
    basicSupport ? 'WebAssembly is supported' : 'WebAssembly is not supported');

  if (!basicSupport) {
    return results;
  }

  // Test 2: WebAssembly.compile
  try {
    const wasmCode = new Uint8Array([
      0x00, 0x61, 0x73, 0x6d, // WASM magic number
      0x01, 0x00, 0x00, 0x00, // WASM version
      0x01, 0x04, 0x01, 0x60, 0x00, 0x00, // Type section
      0x03, 0x02, 0x01, 0x00, // Function section
      0x0a, 0x04, 0x01, 0x02, 0x00, 0x0b // Code section
    ]);
    
    await WebAssembly.compile(wasmCode);
    addTest('WebAssembly.compile', true, 'WASM compilation works with current CSP');
  } catch (error) {
    const isCSPError = error.message.includes('unsafe-eval') || 
                      error.message.includes('Content Security Policy');
    addTest('WebAssembly.compile', false, 
      isCSPError ? 'CSP blocks WASM compilation - unsafe-eval needed' : `Compilation error: ${error.message}`);
  }

  // Test 3: WebAssembly.instantiateStreaming (if supported)
  if (typeof WebAssembly.instantiateStreaming === 'function') {
    try {
      // Create a simple WASM module blob
      const wasmCode = new Uint8Array([
        0x00, 0x61, 0x73, 0x6d, 0x01, 0x00, 0x00, 0x00,
        0x01, 0x04, 0x01, 0x60, 0x00, 0x00,
        0x03, 0x02, 0x01, 0x00,
        0x0a, 0x04, 0x01, 0x02, 0x00, 0x0b
      ]);
      
      const response = new Response(wasmCode, {
        headers: { 'Content-Type': 'application/wasm' }
      });
      
      await WebAssembly.instantiateStreaming(response);
      addTest('WebAssembly.instantiateStreaming', true, 'Streaming instantiation works');
    } catch (error) {
      const isCSPError = error.message.includes('unsafe-eval') || 
                        error.message.includes('Content Security Policy');
      addTest('WebAssembly.instantiateStreaming', false, 
        isCSPError ? 'CSP blocks streaming instantiation' : `Streaming error: ${error.message}`);
    }
  } else {
    addTest('WebAssembly.instantiateStreaming', false, 'Streaming not supported in this browser');
  }

  return results;
};

/**
 * Get recommended CSP for WebAssembly applications
 * @returns {Object} Recommended CSP configuration
 */
export const getWebAssemblyCSPRecommendations = () => {
  return {
    required: {
      'script-src': ["'unsafe-eval'"],
      explanation: "WebAssembly compilation requires 'unsafe-eval' in script-src"
    },
    recommended: {
      'worker-src': ["'self'", 'blob:'],
      explanation: "For WebAssembly workers and blob workers"
    },
    optional: {
      'wasm-unsafe-eval': ["'self'"],
      explanation: "Future CSP directive specifically for WASM (not yet widely supported)"
    },
    security_notes: [
      "unsafe-eval is required for WASM but still blocks most XSS vectors",
      "Consider using nonces for other scripts to maintain security",
      "Monitor CSP violations to detect potential security issues",
      "Keep WASM modules from trusted sources only"
    ]
  };
};

/**
 * Log WebAssembly CSP information for debugging
 */
export const logWebAssemblyCSPInfo = async () => {
  console.group('🔧 WebAssembly CSP Information');
  
  const support = checkWebAssemblySupport();
  console.log('📊 WebAssembly Support:', support);
  
  const testResults = await testWebAssemblyWithCSP();
  console.log('🧪 CSP Test Results:', testResults);
  
  const recommendations = getWebAssemblyCSPRecommendations();
  console.log('💡 CSP Recommendations:', recommendations);
  
  console.groupEnd();
  
  return { support, testResults, recommendations };
};
