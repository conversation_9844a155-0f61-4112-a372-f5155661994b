/**
 * Secure HTML Template with CSP Nonce Support
 * 
 * This template replaces inline scripts and styles with nonce-based alternatives
 * to comply with Content Security Policy requirements.
 */

import { ScrollViewStyleReset } from 'expo-router/html';

/**
 * Generate a secure HTML root component with CSP nonce support
 * @param {string} nonce - The CSP nonce for this request
 * @returns {Function} React component for the HTML root
 */
export const createSecureRoot = (nonce) => {
  return function SecureRoot({ children }) {
    return (
      <html lang="en">
        <head>
          <meta charSet="utf-8" />
          <meta name="apple-itunes-app" content="app-id=6738620563" />
          <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
          <meta
            name="viewport"
            content="width=device-width, initial-scale=1, shrink-to-fit=no"
          />

          {/* CSP Meta Tag */}
          <meta
            httpEquiv="Content-Security-Policy"
            content={`default-src 'self'; script-src 'self' https://widgets.in.webengage.com 'nonce-${nonce}'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https:; font-src 'self' https://fonts.gstatic.com data:; connect-src 'self' https://matiks.com https://dev.matiks.com https://widgets.in.webengage.com wss://matiks.com wss://dev.matiks.com; frame-ancestors 'self'; object-src 'none'; base-uri 'self'; form-action 'self'`}
          />

          <meta name="title" content="Matiks - Gamifying Mental Aptitude" />
          <meta
            name="description"
            content="Matiks enhances mental aptitude with gamified mental math challenges. Play worldwide to improve speed and accuracy. Join now!"
          />
          <meta
            name="keywords"
            content="Matiks, mental aptitude, gamification, mental math, educational games, speed and accuracy, brain games"
          />
          <meta name="robots" content="index, follow" />

          {/* Open Graph Meta Tags */}
          <meta property="og:title" content="Matiks - Gamifying Mental Aptitude" />
          <meta
            property="og:description"
            content="Matiks enhances mental aptitude with gamified mental math challenges. Play worldwide to improve speed and accuracy. Join now!"
          />
          <meta property="og:url" content="https://www.matiks.in" />
          <meta property="og:type" content="website" />

          {/* Twitter Card Meta Tags */}
          <meta name="twitter:card" content="summary_large_image" />
          <meta name="twitter:title" content="Matiks - Gamifying Mental Aptitude" />
          <meta
            name="twitter:description"
            content="Matiks enhances mental aptitude with gamified mental math challenges. Play worldwide to improve speed and accuracy. Join now!"
          />
          <meta name="twitter:site" content="@matiks_play" />

          <ScrollViewStyleReset />

          {/* Secure CSS with nonce */}
          <style nonce={nonce}>
            {`
              body {
                background-color: #fff;
              }
              @media (prefers-color-scheme: dark) {
                body {
                  background-color: #000;
                }
              }
            `}
          </style>

          {/* External scripts that are allowed by CSP */}
          <script
            nonce={nonce}
            src="https://widgets.in.webengage.com/js/webengage-min-v-6.0.js"
            async
          />
          
          {/* WebEngage initialization script with nonce */}
          <script nonce={nonce}>
            {`
              window.webengageConfig = {
                licenseCode: 'in~~c2ab3735'
              };
              
              // Initialize WebEngage when the external script loads
              window.addEventListener('load', function() {
                if (window.webengage) {
                  window.webengage.init(window.webengageConfig.licenseCode);
                }
              });
            `}
          </script>
        </head>
        <body>{children}</body>
      </html>
    );
  };
};

/**
 * Default secure root component (for static generation)
 */
export default function SecureRoot({ children }) {
  // For static generation, we'll use a placeholder nonce
  // In a real server-side rendering scenario, this would be replaced
  const staticNonce = 'STATIC_NONCE_PLACEHOLDER';
  
  const SecureRootComponent = createSecureRoot(staticNonce);
  return <SecureRootComponent>{children}</SecureRootComponent>;
}
