/**
 * Content Security Policy (CSP) Configuration
 *
 * Simple CSP configuration for Matik<PERSON> application.
 * Note: 'unsafe-eval' is required for WebAssembly support.
 */

// CSP Configuration for Matiks
export const CSP_CONFIG = {
  'script-src': [
    "'self'",
    'https://widgets.in.webengage.com',
    "'unsafe-eval'" // Required for WebAssembly
  ],
  'style-src': [
    "'self'",
    'https://fonts.googleapis.com',
    "'unsafe-inline'"
  ],
  'img-src': [
    "'self'",
    'data:',
    'blob:',
    'https:'
  ],
  'font-src': [
    "'self'",
    'https://fonts.gstatic.com',
    'data:'
  ],
  'connect-src': [
    "'self'",
    'https://matiks.com',
    'https://server.matiks.com',
    'https://dev.matiks.com',
    'https://dev.server.matiks.com',
    'https://widgets.in.webengage.com',
    'https://api.mixpanel.com',
    'wss://matiks.com',
    'wss://server.matiks.com',
    'wss://dev.matiks.com',
    'wss://dev.server.matiks.com',
    'ws://localhost:*',
    'wss://localhost:*',
    'http://localhost:*',
    'https://localhost:*',
    'http://localhost:4000',
    'https://localhost:4000'
  ],
  'frame-ancestors': ["'self'"],
  'object-src': ["'none'"],
  'base-uri': ["'self'"],
  'form-action': ["'self'"]
};
