/**
 * Content Security Policy (CSP) Configuration
 *
 * This module provides CSP configuration for blocking inline scripts and enhancing security.
 * CSP helps prevent XSS attacks, data injection, and other code injection vulnerabilities.
 *
 * Note: WebAssembly Support
 * WebAssembly requires 'unsafe-eval' in script-src directive because WASM compilation
 * is considered similar to eval() from a security perspective. This is a necessary
 * trade-off for WASM functionality while still blocking most XSS vectors.
 */

import crypto from 'crypto';

/**
 * Generate a cryptographically secure nonce for CSP
 * @returns {string} Base64 encoded nonce
 */
export const generateNonce = () => {
  return crypto.randomBytes(16).toString('base64');
};

/**
 * CSP Configuration Object
 * This defines what sources are allowed for different types of content
 */
export const CSP_CONFIG = {
  // Allow scripts only from self, specific CDNs, and with nonce
  // Note: 'unsafe-eval' is required for WebAssembly support
  'script-src': [
    "'self'",
    'https://widgets.in.webengage.com',
    'https://cdn.jsdelivr.net',
    'https://unpkg.com',
    // Required for WebAssembly compilation
    "'unsafe-eval'",
    // Remove 'unsafe-inline' to block inline scripts
    // "'unsafe-inline'", // REMOVED FOR SECURITY
    // Allow scripts with nonce only
    "'nonce-{NONCE}'"
  ],

  // Allow styles from self, inline styles with nonce, and specific CDNs
  'style-src': [
    "'self'",
    "'nonce-{NONCE}'",
    'https://fonts.googleapis.com',
    'https://cdn.jsdelivr.net',
    // Allow unsafe-inline for styles (less risky than scripts)
    "'unsafe-inline'"
  ],

  // Allow images from multiple sources
  'img-src': [
    "'self'",
    'data:',
    'blob:',
    'https:',
    'https://www.matiks.in',
    'https://cdn.matiks.com'
  ],

  // Allow fonts from self and Google Fonts
  'font-src': [
    "'self'",
    'https://fonts.gstatic.com',
    'data:'
  ],

  // Allow connections to API endpoints and WebSocket
  'connect-src': [
    "'self'",
    'https://matiks.com',
    'https://dev.matiks.com',
    'https://widgets.in.webengage.com',
    'wss://matiks.com',
    'wss://dev.matiks.com',
    'ws://localhost:*',
    'http://localhost:*'
  ],

  // Prevent framing except from same origin
  'frame-ancestors': ["'self'"],

  // Allow media from self and data URLs
  'media-src': ["'self'", 'data:', 'blob:'],

  // Allow objects from self only
  'object-src': ["'none'"],

  // Base URI restrictions
  'base-uri': ["'self'"],

  // Form action restrictions
  'form-action': ["'self'"],

  // Upgrade insecure requests in production
  'upgrade-insecure-requests': true,

  // Block mixed content
  'block-all-mixed-content': true
};

/**
 * Generate CSP header string with nonce
 * @param {string} nonce - The nonce to use for this request
 * @returns {string} CSP header value
 */
export const generateCSPHeader = (nonce) => {
  const directives = [];

  Object.entries(CSP_CONFIG).forEach(([directive, values]) => {
    if (typeof values === 'boolean' && values) {
      // For boolean directives like upgrade-insecure-requests
      directives.push(directive);
    } else if (Array.isArray(values)) {
      // Replace nonce placeholder with actual nonce
      const processedValues = values.map(value =>
        value.replace('{NONCE}', nonce)
      );
      directives.push(`${directive} ${processedValues.join(' ')}`);
    }
  });

  return directives.join('; ');
};

/**
 * CSP Violation Report Handler
 * This function processes CSP violation reports sent by the browser
 */
export const handleCSPViolation = (violationReport) => {
  console.warn('CSP Violation:', {
    blockedURI: violationReport['blocked-uri'],
    violatedDirective: violationReport['violated-directive'],
    originalPolicy: violationReport['original-policy'],
    documentURI: violationReport['document-uri'],
    sourceFile: violationReport['source-file'],
    lineNumber: violationReport['line-number'],
    columnNumber: violationReport['column-number']
  });

  // In production, you might want to send this to your logging service
  if (process.env.NODE_ENV === 'production') {
    // Example: Send to your analytics/logging service
    // analytics.track('CSP_Violation', violationReport);
  }
};

/**
 * Development mode CSP (more permissive for development)
 */
export const DEV_CSP_CONFIG = {
  ...CSP_CONFIG,
  'script-src': [
    "'self'",
    "'unsafe-inline'", // Allow inline scripts in development
    "'unsafe-eval'", // Allow eval in development (for hot reloading)
    'https://widgets.in.webengage.com',
    'http://localhost:*',
    'ws://localhost:*'
  ],
  'connect-src': [
    ...CSP_CONFIG['connect-src'],
    'http://localhost:*',
    'ws://localhost:*'
  ]
};

/**
 * Get appropriate CSP config based on environment
 */
export const getCSPConfig = () => {
  return process.env.NODE_ENV === 'development' ? DEV_CSP_CONFIG : CSP_CONFIG;
};
