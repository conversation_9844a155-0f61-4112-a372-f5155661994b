/**
 * CSP Testing Utility
 *
 * This utility helps test Content Security Policy implementation
 * and identify potential violations during development.
 */

/**
 * Test CSP implementation by attempting various potentially unsafe operations
 * @returns {Object} Test results
 */
export const testCSP = () => {
  const results = {
    timestamp: new Date().toISOString(),
    tests: [],
    summary: {
      passed: 0,
      failed: 0,
      total: 0
    }
  };

  const addTest = (name, passed, details) => {
    results.tests.push({
      name,
      passed,
      details,
      timestamp: new Date().toISOString()
    });

    if (passed) {
      results.summary.passed++;
    } else {
      results.summary.failed++;
    }
    results.summary.total++;
  };

  // Test 1: Inline script execution (should be blocked)
  try {
    const script = document.createElement('script');
    script.textContent = 'window.cspTestInlineScript = true;';
    document.head.appendChild(script);
    document.head.removeChild(script);

    const blocked = !window.cspTestInlineScript;
    addTest('Inline Script Blocking', blocked,
      blocked ? 'Inline scripts are properly blocked' : 'WARNING: Inline scripts are allowed');
  } catch (error) {
    addTest('Inline Script Blocking', true, 'Inline scripts blocked with error: ' + error.message);
  }

  // Test 2: eval() execution (should be blocked)
  try {
    eval('window.cspTestEval = true;');
    const blocked = !window.cspTestEval;
    addTest('eval() Blocking', blocked,
      blocked ? 'eval() is properly blocked' : 'WARNING: eval() is allowed');
  } catch (error) {
    addTest('eval() Blocking', true, 'eval() blocked with error: ' + error.message);
  }

  // Test 3: Function constructor (should be blocked)
  try {
    const func = new Function('window.cspTestFunction = true;');
    func();
    const blocked = !window.cspTestFunction;
    addTest('Function Constructor Blocking', blocked,
      blocked ? 'Function constructor is properly blocked' : 'WARNING: Function constructor is allowed');
  } catch (error) {
    addTest('Function Constructor Blocking', true, 'Function constructor blocked with error: ' + error.message);
  }

  // Test 4: setTimeout with string (should be blocked)
  try {
    setTimeout('window.cspTestSetTimeout = true;', 0);
    setTimeout(() => {
      const blocked = !window.cspTestSetTimeout;
      addTest('setTimeout String Blocking', blocked,
        blocked ? 'setTimeout with string is properly blocked' : 'WARNING: setTimeout with string is allowed');
    }, 100);
  } catch (error) {
    addTest('setTimeout String Blocking', true, 'setTimeout with string blocked with error: ' + error.message);
  }

  // Test 5: Check CSP header presence
  const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
  const hasCspHeader = !!cspMeta;
  addTest('CSP Header Present', hasCspHeader,
    hasCspHeader ? 'CSP meta tag found' : 'No CSP meta tag found');

  // Test 6: Check for unsafe-inline in script-src
  if (cspMeta) {
    const cspContent = cspMeta.getAttribute('content');
    const hasUnsafeInline = cspContent.includes("'unsafe-inline'") && cspContent.includes('script-src');
    addTest('No unsafe-inline in script-src', !hasUnsafeInline,
      hasUnsafeInline ? 'WARNING: unsafe-inline found in script-src' : 'script-src does not contain unsafe-inline');
  }

  // Test 7: Check for nonce usage
  const scriptsWithNonce = document.querySelectorAll('script[nonce]');
  const hasNonceScripts = scriptsWithNonce.length > 0;
  addTest('Nonce Usage', hasNonceScripts,
    hasNonceScripts ? `Found ${scriptsWithNonce.length} scripts with nonce` : 'No scripts with nonce found');

  // Test 8: WebAssembly support check
  const hasWebAssembly = typeof WebAssembly === 'object';
  if (hasWebAssembly && cspMeta) {
    const cspContent = cspMeta.getAttribute('content');
    const hasUnsafeEval = cspContent.includes("'unsafe-eval'");
    addTest('WebAssembly CSP Support', hasUnsafeEval,
      hasUnsafeEval ? 'CSP includes unsafe-eval for WebAssembly' : 'WARNING: WebAssembly may not work - unsafe-eval missing');
  } else if (hasWebAssembly) {
    addTest('WebAssembly CSP Support', false, 'No CSP found - WebAssembly support unknown');
  }

  return results;
};

/**
 * Run CSP tests and log results
 */
export const runCSPTests = () => {
  console.group('🔒 CSP Security Tests');

  const results = testCSP();

  console.log('📊 Test Summary:', results.summary);

  results.tests.forEach(test => {
    const icon = test.passed ? '✅' : '❌';
    const style = test.passed ? 'color: green' : 'color: red';
    console.log(`${icon} %c${test.name}`, style, test.details);
  });

  if (results.summary.failed > 0) {
    console.warn(`⚠️  ${results.summary.failed} tests failed. Review CSP configuration.`);
  } else {
    console.log('🎉 All CSP tests passed!');
  }

  console.groupEnd();

  return results;
};

/**
 * Monitor CSP violations in real-time
 */
export const startCSPMonitoring = () => {
  let violationCount = 0;

  const handleViolation = (event) => {
    violationCount++;

    console.group(`🚨 CSP Violation #${violationCount}`);
    console.error('Blocked URI:', event.blockedURI);
    console.error('Violated Directive:', event.violatedDirective);
    console.error('Source File:', event.sourceFile);
    console.error('Line Number:', event.lineNumber);
    console.error('Column Number:', event.columnNumber);
    console.error('Sample:', event.sample);
    console.groupEnd();
  };

  document.addEventListener('securitypolicyviolation', handleViolation);

  console.log('🔍 CSP monitoring started. Violations will be logged to console.');

  // Return cleanup function
  return () => {
    document.removeEventListener('securitypolicyviolation', handleViolation);
    console.log('🔍 CSP monitoring stopped.');
  };
};

/**
 * Generate CSP report for current page
 */
export const generateCSPReport = () => {
  const report = {
    timestamp: new Date().toISOString(),
    url: window.location.href,
    userAgent: navigator.userAgent,
    csp: {
      present: false,
      content: null,
      directives: {}
    },
    scripts: [],
    styles: [],
    images: [],
    recommendations: []
  };

  // Check CSP presence
  const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
  if (cspMeta) {
    report.csp.present = true;
    report.csp.content = cspMeta.getAttribute('content');

    // Parse directives
    const directives = report.csp.content.split(';');
    directives.forEach(directive => {
      const [key, ...values] = directive.trim().split(/\s+/);
      if (key) {
        report.csp.directives[key] = values;
      }
    });
  }

  // Analyze scripts
  document.querySelectorAll('script').forEach((script, index) => {
    report.scripts.push({
      index,
      src: script.src || null,
      inline: !script.src,
      hasNonce: !!script.getAttribute('nonce'),
      nonce: script.getAttribute('nonce'),
      async: script.async,
      defer: script.defer
    });
  });

  // Analyze styles
  document.querySelectorAll('style, link[rel="stylesheet"]').forEach((style, index) => {
    report.styles.push({
      index,
      href: style.href || null,
      inline: style.tagName === 'STYLE',
      hasNonce: !!style.getAttribute('nonce'),
      nonce: style.getAttribute('nonce')
    });
  });

  // Analyze images
  document.querySelectorAll('img').forEach((img, index) => {
    report.images.push({
      index,
      src: img.src,
      protocol: new URL(img.src, window.location.href).protocol
    });
  });

  // Generate recommendations
  if (!report.csp.present) {
    report.recommendations.push('Add Content-Security-Policy meta tag or header');
  }

  if (report.csp.directives['script-src']?.includes("'unsafe-inline'")) {
    report.recommendations.push('Remove unsafe-inline from script-src and use nonces instead');
  }

  const inlineScriptsWithoutNonce = report.scripts.filter(s => s.inline && !s.hasNonce);
  if (inlineScriptsWithoutNonce.length > 0) {
    report.recommendations.push(`Add nonces to ${inlineScriptsWithoutNonce.length} inline scripts`);
  }

  return report;
};
