/**
 * CSP Middleware for Express/Next.js applications
 * 
 * This middleware adds CSP headers to all responses and handles nonce generation
 */

import { generateNonce, generateCSPHeader, getCSPConfig } from './csp.js';

/**
 * CSP Middleware function
 * Adds Content-Security-Policy headers to responses
 */
export const cspMiddleware = (req, res, next) => {
  // Generate a unique nonce for this request
  const nonce = generateNonce();
  
  // Store nonce in request object for use in templates
  req.nonce = nonce;
  
  // Generate CSP header
  const cspHeader = generateCSPHeader(nonce);
  
  // Set CSP header
  res.setHeader('Content-Security-Policy', cspHeader);
  
  // Also set CSP report-only header for monitoring (optional)
  if (process.env.NODE_ENV === 'production') {
    res.setHeader('Content-Security-Policy-Report-Only', cspHeader);
  }
  
  // Set other security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'SAMEORIGIN');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // HSTS header for HTTPS
  if (req.secure || req.headers['x-forwarded-proto'] === 'https') {
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  }
  
  next();
};

/**
 * CSP Violation Report Endpoint
 * Handles CSP violation reports sent by browsers
 */
export const cspReportHandler = (req, res) => {
  try {
    const report = req.body;
    
    if (report && report['csp-report']) {
      const violation = report['csp-report'];
      
      // Log the violation
      console.warn('CSP Violation Report:', {
        timestamp: new Date().toISOString(),
        userAgent: req.headers['user-agent'],
        ip: req.ip,
        violation: {
          blockedURI: violation['blocked-uri'],
          violatedDirective: violation['violated-directive'],
          originalPolicy: violation['original-policy'],
          documentURI: violation['document-uri'],
          sourceFile: violation['source-file'],
          lineNumber: violation['line-number'],
          columnNumber: violation['column-number']
        }
      });
      
      // In production, send to monitoring service
      if (process.env.NODE_ENV === 'production') {
        // Example: Send to your monitoring service
        // monitoringService.reportCSPViolation(violation);
      }
    }
    
    res.status(204).end();
  } catch (error) {
    console.error('Error processing CSP violation report:', error);
    res.status(400).json({ error: 'Invalid report format' });
  }
};
