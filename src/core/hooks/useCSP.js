/**
 * React Hook for CSP Management
 * 
 * This hook provides utilities for working with Content Security Policy
 * in React components, including nonce management and violation handling.
 */

import { useEffect, useCallback, useState } from 'react';

/**
 * Hook for managing CSP in React components
 * @returns {Object} CSP utilities and state
 */
export const useCSP = () => {
  const [violations, setViolations] = useState([]);
  const [isCSPEnabled, setIsCSPEnabled] = useState(false);

  // Check if CSP is enabled
  useEffect(() => {
    const checkCSP = () => {
      // Check if CSP meta tag exists
      const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
      setIsCSPEnabled(!!cspMeta);
    };

    checkCSP();
  }, []);

  // Listen for CSP violations
  useEffect(() => {
    const handleSecurityPolicyViolation = (event) => {
      const violation = {
        timestamp: new Date().toISOString(),
        blockedURI: event.blockedURI,
        violatedDirective: event.violatedDirective,
        originalPolicy: event.originalPolicy,
        documentURI: event.documentURI,
        sourceFile: event.sourceFile,
        lineNumber: event.lineNumber,
        columnNumber: event.columnNumber,
        sample: event.sample
      };

      setViolations(prev => [...prev, violation]);

      // Log violation for debugging
      console.warn('CSP Violation detected:', violation);

      // Send violation report to server
      sendViolationReport(violation);
    };

    // Listen for CSP violations
    document.addEventListener('securitypolicyviolation', handleSecurityPolicyViolation);

    return () => {
      document.removeEventListener('securitypolicyviolation', handleSecurityPolicyViolation);
    };
  }, []);

  // Send violation report to server
  const sendViolationReport = useCallback(async (violation) => {
    try {
      await fetch('/api/csp-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          'csp-report': violation
        })
      });
    } catch (error) {
      console.error('Failed to send CSP violation report:', error);
    }
  }, []);

  // Get nonce from meta tag (if available)
  const getNonce = useCallback(() => {
    const nonceMeta = document.querySelector('meta[name="csp-nonce"]');
    return nonceMeta ? nonceMeta.getAttribute('content') : null;
  }, []);

  // Check if a script source is allowed by CSP
  const isScriptAllowed = useCallback((src) => {
    if (!isCSPEnabled) return true;

    const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    if (!cspMeta) return true;

    const cspContent = cspMeta.getAttribute('content');
    const scriptSrcMatch = cspContent.match(/script-src\s+([^;]+)/);
    
    if (!scriptSrcMatch) return false;

    const allowedSources = scriptSrcMatch[1].split(/\s+/);
    
    // Check if source is explicitly allowed
    return allowedSources.some(source => {
      if (source === "'self'" && src.startsWith(window.location.origin)) return true;
      if (source === "'unsafe-inline'") return true;
      if (source.startsWith('http') && src.startsWith(source)) return true;
      return false;
    });
  }, [isCSPEnabled]);

  // Safely execute inline script with nonce
  const executeInlineScript = useCallback((scriptContent, nonce) => {
    if (!isCSPEnabled) {
      // If CSP is not enabled, execute directly
      const script = document.createElement('script');
      script.textContent = scriptContent;
      document.head.appendChild(script);
      document.head.removeChild(script);
      return;
    }

    // With CSP enabled, use nonce
    const script = document.createElement('script');
    script.textContent = scriptContent;
    if (nonce) {
      script.setAttribute('nonce', nonce);
    }
    document.head.appendChild(script);
    document.head.removeChild(script);
  }, [isCSPEnabled]);

  // Clear violations history
  const clearViolations = useCallback(() => {
    setViolations([]);
  }, []);

  return {
    isCSPEnabled,
    violations,
    getNonce,
    isScriptAllowed,
    executeInlineScript,
    clearViolations,
    violationCount: violations.length
  };
};

/**
 * Hook for CSP nonce in components
 * @returns {string|null} Current nonce value
 */
export const useCSPNonce = () => {
  const [nonce, setNonce] = useState(null);

  useEffect(() => {
    // Try to get nonce from meta tag
    const nonceMeta = document.querySelector('meta[name="csp-nonce"]');
    if (nonceMeta) {
      setNonce(nonceMeta.getAttribute('content'));
    }

    // Try to get nonce from script tag
    const scripts = document.querySelectorAll('script[nonce]');
    if (scripts.length > 0) {
      setNonce(scripts[0].getAttribute('nonce'));
    }
  }, []);

  return nonce;
};
