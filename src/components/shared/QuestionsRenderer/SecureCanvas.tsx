import useUserStore from 'store/useUserStore';
import { useRef, useEffect } from 'react';
import { draw_question } from 'wasm/index';
import { View } from 'react-native';

const SecureCanvas = ({
  width = 400,
  height = 400,
  questionId = '',
  renderQuestionOverlay,
}: any) => {
  const wrapperRef = useRef(null);
  const canvasRef = useRef(document.createElement('canvas'));
  const { isWasmReady } = useUserStore((state) => ({
    isWasmReady: state.isWasmReady,
  }));

  const isAlreadyShadowed = useRef(false);

  useEffect(() => {
    if (!isWasmReady) return;
    const canvas = canvasRef.current;
    canvas.width = width;
    canvas.height = height;

    const shadowHost = wrapperRef.current;
    if (!shadowHost) return;
    if (!isAlreadyShadowed.current) {
      const shadow = (shadowHost as HTMLDivElement)?.attachShadow({
        mode: 'closed',
      });
      shadow?.appendChild(canvas);
      isAlreadyShadowed.current = true;
    }

    const ctx = canvas.getContext('2d');
    const render = () => {
      if (!ctx || !questionId) return;
      draw_question(questionId, canvasRef.current, ctx);
    };

    render();

    const fakeBlob = new Blob(['Access Denied'], { type: 'text/plain' });
    canvas.toBlob = (cb) => {
      cb(fakeBlob);
    };

    canvas.toDataURL = () => {
      return 'data:image/png;base64,';
    };
  }, [width, height, isWasmReady, questionId]);

  return (
    <View
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <div
        ref={wrapperRef}
        style={{
          width,
          height,
          display: 'inline-block',
          overflow: 'hidden',
          userSelect: 'none',
          pointerEvents: 'none',
        }}
      />
      {renderQuestionOverlay()}
    </View>
  );
};

export default SecureCanvas;
