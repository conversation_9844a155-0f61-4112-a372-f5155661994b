/**
 * CSP Example Component
 * 
 * This component demonstrates how to use CSP utilities in React components
 * and shows the security status of the application.
 */

import React, { useState, useEffect } from 'react';
import { useCSP } from '../../core/hooks/useCSP';
import { runCSPTests, generateCSPReport } from '../../core/security/cspTester';

const CSPExample = () => {
  const { isCSPEnabled, violations, violationCount, clearViolations } = useCSP();
  const [testResults, setTestResults] = useState(null);
  const [cspReport, setCSPReport] = useState(null);
  const [showDetails, setShowDetails] = useState(false);

  // Run CSP tests on component mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Run tests after a short delay to ensure DOM is ready
      setTimeout(() => {
        const results = runCSPTests();
        setTestResults(results);
        
        const report = generateCSPReport();
        setCSPReport(report);
      }, 1000);
    }
  }, []);

  const handleRunTests = () => {
    if (typeof window !== 'undefined') {
      const results = runCSPTests();
      setTestResults(results);
    }
  };

  const handleGenerateReport = () => {
    if (typeof window !== 'undefined') {
      const report = generateCSPReport();
      setCSPReport(report);
      console.log('CSP Report:', report);
    }
  };

  const getSecurityStatus = () => {
    if (!testResults) return 'Unknown';
    
    const passRate = (testResults.summary.passed / testResults.summary.total) * 100;
    
    if (passRate >= 90) return 'Excellent';
    if (passRate >= 70) return 'Good';
    if (passRate >= 50) return 'Fair';
    return 'Poor';
  };

  const getStatusColor = () => {
    const status = getSecurityStatus();
    switch (status) {
      case 'Excellent': return '#4CAF50';
      case 'Good': return '#8BC34A';
      case 'Fair': return '#FF9800';
      case 'Poor': return '#F44336';
      default: return '#9E9E9E';
    }
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h2>🔒 Content Security Policy Status</h2>
      
      {/* Security Overview */}
      <div style={{ 
        backgroundColor: '#f5f5f5', 
        padding: '15px', 
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        <h3>Security Overview</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
          <div>
            <strong>CSP Status:</strong>
            <span style={{ 
              color: isCSPEnabled ? '#4CAF50' : '#F44336',
              marginLeft: '10px'
            }}>
              {isCSPEnabled ? '✅ Enabled' : '❌ Disabled'}
            </span>
          </div>
          
          <div>
            <strong>Security Level:</strong>
            <span style={{ 
              color: getStatusColor(),
              marginLeft: '10px'
            }}>
              {getSecurityStatus()}
            </span>
          </div>
          
          <div>
            <strong>Violations:</strong>
            <span style={{ 
              color: violationCount > 0 ? '#FF9800' : '#4CAF50',
              marginLeft: '10px'
            }}>
              {violationCount}
            </span>
          </div>
        </div>
      </div>

      {/* Test Results */}
      {testResults && (
        <div style={{ 
          backgroundColor: '#e8f5e8', 
          padding: '15px', 
          borderRadius: '8px',
          marginBottom: '20px'
        }}>
          <h3>Test Results</h3>
          <div style={{ marginBottom: '10px' }}>
            <strong>Summary:</strong> {testResults.summary.passed}/{testResults.summary.total} tests passed
          </div>
          
          <button 
            onClick={() => setShowDetails(!showDetails)}
            style={{
              backgroundColor: '#2196F3',
              color: 'white',
              border: 'none',
              padding: '8px 16px',
              borderRadius: '4px',
              cursor: 'pointer',
              marginRight: '10px'
            }}
          >
            {showDetails ? 'Hide Details' : 'Show Details'}
          </button>
          
          {showDetails && (
            <div style={{ marginTop: '15px' }}>
              {testResults.tests.map((test, index) => (
                <div key={index} style={{ 
                  padding: '8px',
                  margin: '5px 0',
                  backgroundColor: test.passed ? '#d4edda' : '#f8d7da',
                  borderRadius: '4px'
                }}>
                  <span style={{ marginRight: '10px' }}>
                    {test.passed ? '✅' : '❌'}
                  </span>
                  <strong>{test.name}:</strong> {test.details}
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Violations */}
      {violations.length > 0 && (
        <div style={{ 
          backgroundColor: '#fff3cd', 
          padding: '15px', 
          borderRadius: '8px',
          marginBottom: '20px'
        }}>
          <h3>⚠️ CSP Violations</h3>
          <p>The following violations have been detected:</p>
          
          {violations.slice(-5).map((violation, index) => (
            <div key={index} style={{ 
              backgroundColor: '#f8d7da',
              padding: '10px',
              margin: '5px 0',
              borderRadius: '4px',
              fontSize: '14px'
            }}>
              <div><strong>Blocked URI:</strong> {violation.blockedURI}</div>
              <div><strong>Violated Directive:</strong> {violation.violatedDirective}</div>
              <div><strong>Time:</strong> {violation.timestamp}</div>
            </div>
          ))}
          
          {violations.length > 5 && (
            <p style={{ fontStyle: 'italic' }}>
              Showing last 5 violations. Total: {violations.length}
            </p>
          )}
          
          <button 
            onClick={clearViolations}
            style={{
              backgroundColor: '#dc3545',
              color: 'white',
              border: 'none',
              padding: '8px 16px',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Clear Violations
          </button>
        </div>
      )}

      {/* Actions */}
      <div style={{ 
        display: 'flex', 
        gap: '10px', 
        flexWrap: 'wrap',
        marginBottom: '20px'
      }}>
        <button 
          onClick={handleRunTests}
          style={{
            backgroundColor: '#4CAF50',
            color: 'white',
            border: 'none',
            padding: '10px 20px',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          🧪 Run CSP Tests
        </button>
        
        <button 
          onClick={handleGenerateReport}
          style={{
            backgroundColor: '#2196F3',
            color: 'white',
            border: 'none',
            padding: '10px 20px',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          📊 Generate Report
        </button>
      </div>

      {/* CSP Report Summary */}
      {cspReport && (
        <div style={{ 
          backgroundColor: '#e3f2fd', 
          padding: '15px', 
          borderRadius: '8px'
        }}>
          <h3>📊 CSP Report Summary</h3>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '10px' }}>
            <div><strong>Scripts:</strong> {cspReport.scripts.length}</div>
            <div><strong>Styles:</strong> {cspReport.styles.length}</div>
            <div><strong>Images:</strong> {cspReport.images.length}</div>
            <div><strong>CSP Present:</strong> {cspReport.csp.present ? '✅' : '❌'}</div>
          </div>
          
          {cspReport.recommendations.length > 0 && (
            <div style={{ marginTop: '15px' }}>
              <strong>Recommendations:</strong>
              <ul>
                {cspReport.recommendations.map((rec, index) => (
                  <li key={index}>{rec}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Development Note */}
      <div style={{ 
        marginTop: '20px',
        padding: '10px',
        backgroundColor: '#f0f0f0',
        borderRadius: '4px',
        fontSize: '14px',
        color: '#666'
      }}>
        <strong>Note:</strong> This component is for development and testing purposes. 
        In production, CSP violations should be monitored through your logging and 
        monitoring infrastructure.
      </div>
    </div>
  );
};

export default CSPExample;
