#!/usr/bin/env node

/**
 * WebAssembly CSP Testing Script
 * 
 * This script creates a simple test to verify that WebAssembly works
 * with the current CSP configuration.
 */

const fs = require('fs');

console.log('🔧 Testing WebAssembly CSP Compatibility...\n');

// Check if CSP includes unsafe-eval
console.log('📋 Checking CSP Configuration...');

const htmlFiles = ['public/index.html', 'app/+html.jsx'];
let cspFound = false;
let hasUnsafeEval = false;

htmlFiles.forEach(file => {
  if (fs.existsSync(file)) {
    const content = fs.readFileSync(file, 'utf8');
    if (content.includes('Content-Security-Policy')) {
      cspFound = true;
      if (content.includes("'unsafe-eval'")) {
        hasUnsafeEval = true;
        console.log(`✅ ${file}: CSP includes 'unsafe-eval' for WebAssembly`);
      } else {
        console.log(`❌ ${file}: CSP missing 'unsafe-eval' - WebAssembly will fail`);
      }
    }
  }
});

// Check Vercel configuration
if (fs.existsSync('vercel.json')) {
  const vercelConfig = JSON.parse(fs.readFileSync('vercel.json', 'utf8'));
  const vercelContent = JSON.stringify(vercelConfig);
  if (vercelContent.includes('Content-Security-Policy')) {
    if (vercelContent.includes("'unsafe-eval'")) {
      console.log('✅ vercel.json: CSP includes unsafe-eval for WebAssembly');
    } else {
      console.log('❌ vercel.json: CSP missing unsafe-eval - WebAssembly will fail');
      hasUnsafeEval = false;
    }
  }
}

// Create a simple HTML test file
console.log('\n🧪 Creating WebAssembly Test File...');

const testHTML = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebAssembly CSP Test</title>
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-eval'; style-src 'self' 'unsafe-inline';">
</head>
<body>
    <h1>WebAssembly CSP Test</h1>
    <div id="result">Testing...</div>
    
    <script>
        async function testWebAssembly() {
            const resultDiv = document.getElementById('result');
            
            try {
                // Simple WebAssembly module that exports an add function
                const wasmCode = new Uint8Array([
                    0x00, 0x61, 0x73, 0x6d, // WASM magic number
                    0x01, 0x00, 0x00, 0x00, // WASM version
                    0x01, 0x07, 0x01, 0x60, 0x02, 0x7f, 0x7f, 0x01, 0x7f, // Type section: (i32, i32) -> i32
                    0x03, 0x02, 0x01, 0x00, // Function section
                    0x07, 0x07, 0x01, 0x03, 0x61, 0x64, 0x64, 0x00, 0x00, // Export section: export "add" function
                    0x0a, 0x09, 0x01, 0x07, 0x00, 0x20, 0x00, 0x20, 0x01, 0x6a, 0x0b // Code section: add function
                ]);
                
                console.log('Compiling WebAssembly module...');
                const wasmModule = await WebAssembly.compile(wasmCode);
                
                console.log('Instantiating WebAssembly module...');
                const wasmInstance = await WebAssembly.instantiate(wasmModule);
                
                console.log('Testing WebAssembly function...');
                const result = wasmInstance.exports.add(5, 3);
                
                if (result === 8) {
                    resultDiv.innerHTML = '<span style="color: green;">✅ WebAssembly works with CSP! 5 + 3 = ' + result + '</span>';
                    console.log('✅ WebAssembly test passed!');
                } else {
                    resultDiv.innerHTML = '<span style="color: red;">❌ WebAssembly function returned unexpected result: ' + result + '</span>';
                }
                
            } catch (error) {
                console.error('WebAssembly test failed:', error);
                
                if (error.message.includes('unsafe-eval') || error.message.includes('Content Security Policy')) {
                    resultDiv.innerHTML = '<span style="color: red;">❌ CSP blocks WebAssembly: ' + error.message + '</span>';
                } else {
                    resultDiv.innerHTML = '<span style="color: red;">❌ WebAssembly error: ' + error.message + '</span>';
                }
            }
        }
        
        // Run test when page loads
        window.addEventListener('load', testWebAssembly);
    </script>
</body>
</html>`;

fs.writeFileSync('test-wasm-csp.html', testHTML);
console.log('✅ Created test-wasm-csp.html');

// Summary
console.log('\n📊 Summary:');
console.log(`CSP Found: ${cspFound ? '✅' : '❌'}`);
console.log(`unsafe-eval Present: ${hasUnsafeEval ? '✅' : '❌'}`);
console.log(`WebAssembly Compatible: ${hasUnsafeEval ? '✅' : '❌'}`);

console.log('\n🚀 Next Steps:');
console.log('1. Open test-wasm-csp.html in a browser');
console.log('2. Check the browser console for any CSP violations');
console.log('3. Verify that "WebAssembly works with CSP!" message appears');
console.log('4. If test fails, check that unsafe-eval is in script-src directive');

console.log('\n🔍 Manual Testing:');
console.log('You can also test WebAssembly in your browser console:');
console.log('');
console.log('const wasmCode = new Uint8Array([0x00, 0x61, 0x73, 0x6d, 0x01, 0x00, 0x00, 0x00]);');
console.log('WebAssembly.compile(wasmCode).then(() => console.log("✅ WASM works!")).catch(e => console.error("❌ WASM blocked:", e));');

if (!hasUnsafeEval) {
  console.log('\n⚠️  WARNING: Current CSP configuration may block WebAssembly!');
  console.log('Add "\'unsafe-eval\'" to the script-src directive in your CSP policy.');
}
