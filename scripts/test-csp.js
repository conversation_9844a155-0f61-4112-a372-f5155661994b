#!/usr/bin/env node

/**
 * CSP Testing Script
 * 
 * This script tests the CSP implementation by checking various security aspects
 * and generating a comprehensive report.
 */

const fs = require('fs');
const path = require('path');

console.log('🔒 Testing CSP Implementation...\n');

// Test 1: Check if CSP files exist
console.log('📁 Checking CSP files...');
const cspFiles = [
  'src/core/security/csp.js',
  'src/core/security/cspMiddleware.js',
  'src/core/security/cspTester.js',
  'src/core/hooks/useCSP.js',
  'public/js/webengage-init.js',
  'public/api/csp-report.js'
];

let filesExist = 0;
cspFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
    filesExist++;
  } else {
    console.log(`❌ ${file} - Missing`);
  }
});

console.log(`\n📊 Files: ${filesExist}/${cspFiles.length} exist\n`);

// Test 2: Check HTML files for CSP meta tags
console.log('🔍 Checking HTML files for CSP...');
const htmlFiles = ['public/index.html', 'app/+html.jsx'];

htmlFiles.forEach(file => {
  if (fs.existsSync(file)) {
    const content = fs.readFileSync(file, 'utf8');
    const hasCSP = content.includes('Content-Security-Policy');
    const hasInlineScript = content.includes('<script>') && !content.includes('nonce=');
    
    console.log(`📄 ${file}:`);
    console.log(`  CSP Meta Tag: ${hasCSP ? '✅' : '❌'}`);
    console.log(`  Unsafe Inline Scripts: ${hasInlineScript ? '⚠️  Found' : '✅ None'}`);
  }
});

// Test 3: Check Vercel configuration
console.log('\n🚀 Checking Vercel configuration...');
if (fs.existsSync('vercel.json')) {
  const vercelConfig = JSON.parse(fs.readFileSync('vercel.json', 'utf8'));
  const hasCSPHeaders = JSON.stringify(vercelConfig).includes('Content-Security-Policy');
  console.log(`CSP Headers in vercel.json: ${hasCSPHeaders ? '✅' : '❌'}`);
} else {
  console.log('❌ vercel.json not found');
}

// Test 4: Check for inline scripts in public/index.html
console.log('\n🔍 Analyzing public/index.html...');
if (fs.existsSync('public/index.html')) {
  const content = fs.readFileSync('public/index.html', 'utf8');
  
  // Check for inline scripts
  const inlineScriptMatches = content.match(/<script[^>]*>[\s\S]*?<\/script>/gi) || [];
  const inlineScripts = inlineScriptMatches.filter(script => !script.includes('src='));
  
  console.log(`Inline Scripts Found: ${inlineScripts.length}`);
  if (inlineScripts.length > 0) {
    console.log('⚠️  Inline scripts detected:');
    inlineScripts.forEach((script, index) => {
      const preview = script.substring(0, 100) + '...';
      console.log(`  ${index + 1}. ${preview}`);
    });
  } else {
    console.log('✅ No unsafe inline scripts found');
  }
  
  // Check for external scripts
  const externalScripts = content.match(/<script[^>]*src=[^>]*>/gi) || [];
  console.log(`External Scripts: ${externalScripts.length}`);
  externalScripts.forEach(script => {
    console.log(`  📜 ${script}`);
  });
}

// Test 5: Generate recommendations
console.log('\n💡 Recommendations:');

const recommendations = [];

if (filesExist < cspFiles.length) {
  recommendations.push('Complete CSP file implementation');
}

if (fs.existsSync('public/index.html')) {
  const content = fs.readFileSync('public/index.html', 'utf8');
  if (content.includes('<script>') && !content.includes('nonce=')) {
    recommendations.push('Add nonces to inline scripts or move them to external files');
  }
  if (!content.includes('Content-Security-Policy')) {
    recommendations.push('Add CSP meta tag to HTML');
  }
}

if (recommendations.length === 0) {
  console.log('✅ No recommendations - CSP implementation looks good!');
} else {
  recommendations.forEach((rec, index) => {
    console.log(`${index + 1}. ${rec}`);
  });
}

// Test 6: Security score
console.log('\n🏆 Security Score:');
let score = 0;
const maxScore = 10;

// File existence (3 points)
score += (filesExist / cspFiles.length) * 3;

// CSP meta tag (2 points)
if (fs.existsSync('public/index.html')) {
  const content = fs.readFileSync('public/index.html', 'utf8');
  if (content.includes('Content-Security-Policy')) score += 2;
}

// No unsafe inline scripts (3 points)
if (fs.existsSync('public/index.html')) {
  const content = fs.readFileSync('public/index.html', 'utf8');
  const hasUnsafeInline = content.includes('<script>') && !content.includes('nonce=');
  if (!hasUnsafeInline) score += 3;
}

// Vercel headers (2 points)
if (fs.existsSync('vercel.json')) {
  const vercelConfig = JSON.parse(fs.readFileSync('vercel.json', 'utf8'));
  if (JSON.stringify(vercelConfig).includes('Content-Security-Policy')) score += 2;
}

const percentage = Math.round((score / maxScore) * 100);
console.log(`Score: ${score}/${maxScore} (${percentage}%)`);

if (percentage >= 90) {
  console.log('🎉 Excellent CSP implementation!');
} else if (percentage >= 70) {
  console.log('👍 Good CSP implementation with room for improvement');
} else if (percentage >= 50) {
  console.log('⚠️  Basic CSP implementation - needs improvement');
} else {
  console.log('❌ Poor CSP implementation - significant work needed');
}

console.log('\n🔒 CSP Testing Complete!');
console.log('\nNext steps:');
console.log('1. Run the application and check browser console for CSP violations');
console.log('2. Use the CSP testing utilities in the browser');
console.log('3. Monitor violation reports in production');
console.log('4. Regularly audit and update CSP policies');
