<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta content="IE=edge" httpEquiv="X-UA-Compatible" />
  <meta content="width=device-width, initial-scale=1, shrink-to-fit=no" name="viewport" />
  <title>Matiks - Mental Math Duels & Puzzle Battles | Train Your Brain</title>

  <meta content="Matiks - Mental Math Duels & Puzzle Battles | Train Your Brain" name="title" />
  <meta
    content="Challenge players worldwide in fast-paced math duels and puzzle battles on Matiks! Compete for global rankings, improve your speed & accuracy, and climb the leaderboard—just like chess, but with math!"
    name="description" />
  <meta
    content="Matiks, mental aptitude, gamification, mental math, educational games, speed and accuracy, brain games"
    name="keywords" />
  <meta content="index, follow" name="robots" />

  <!-- Open Graph Meta Tags -->
  <meta content="Matiks - Mental Math Duels & Puzzle Battles | Train Your Brain" property="og:title" />
  <meta
    content="Challenge players worldwide in fast-paced math duels and puzzle battles on Matiks! Compete for global rankings, improve your speed & accuracy, and climb the leaderboard—just like chess, but with math!"
    property="og:description" />
  <meta content="https://www.matiks.in" property="og:url" />
  <meta content="https://www.matiks.in/icon.png" property="og:image" />
  <meta content="website" property="og:type" />

  <meta
    content="Challenge players worldwide in fast-paced math duels and puzzle battles on Matiks! Compete for global rankings, improve your speed & accuracy, and climb the leaderboard—just like chess, but with math!"
    property="og:description" />
  <meta content="https://www.matiks.com" property="og:url" />
  <meta content="https://www.matiks.in/icon.png" property="og:image" />
  <meta content="website" property="og:type" />
  <meta content="origin" name="referrer">

  <!-- Twitter Card Meta Tags -->
  <meta content="summary_large_image" name="twitter:card" />
  <meta content="Matiks - Mental Math Duels & Puzzle Battles | Train Your Brain" name="twitter:title" />
  <meta
    content="Challenge players worldwide in fast-paced math duels and puzzle battles on Matiks! Compete for global rankings, improve your speed & accuracy, and climb the leaderboard—just like chess, but with math!"
    name="twitter:description" />
  <meta content="@matiks_play" name="twitter:site" />
  <meta content="https://www.matiks.in/icon.png" name="twitter:image" />
  <link href="favicon.ico" rel="shortcut icon" type="image/x-icon" />
  <link href="/manifest.json" rel="manifest">
  <link href="favicon.ico" rel="icon" type="image/x-icon" />
  <link href="katex/Katex.min.css" rel="stylesheet">
  <script defer src="katex/Katex.min.js"></script>
  <!-- The `react-native-web` recommended style reset: https://necolas.github.io/react-native-web/docs/setup/#root-element -->
  <style id="expo-reset">
      /* These styles make the body full-height */
      html,
      body {
          height: 100%;
      }

      /* These styles disable body scrolling if you are using <ScrollView> */
      body {
          overflow: hidden;
      }

      /* These styles make the root element full-height */
      #root {
          display: flex;
          height: 100%;
          flex: 1;
      }
  </style>
  <!-- CSP Meta Tag for Security -->
  <meta content="default-src 'self'; script-src 'self' https://widgets.in.webengage.com 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https:; font-src 'self' https://fonts.gstatic.com data:; connect-src 'self' https://matiks.com https://server.matiks.com https://dev.matiks.com https://dev.server.matiks.com https://widgets.in.webengage.com https://api.mixpanel.com wss://matiks.com wss://server.matiks.com wss://dev.matiks.com wss://dev.server.matiks.com ws://localhost:* wss://localhost:* http://localhost:* https://localhost:* http://localhost:4000 https://localhost:4000; frame-ancestors 'self'; object-src 'none'; base-uri 'self'; form-action 'self'"
        http-equiv="Content-Security-Policy">

  <script id='_webengage_script_tag' type='text/javascript'>
    var webengage
    !function(w, e, b, n, g) {
      function o(e, t) {
        e[t[t.length - 1]] = function() {
          r.__queue.push([t.join('.'),
            arguments])
        }
      }

      var i, s, r = w[b], z = ' ', l = 'init options track screen onReady'.split(z),
        a = 'webPersonalization feedback survey notification notificationInbox'.split(z),
        c = 'options render clear abort'.split(z),
        p = 'Prepare Render Open Close Submit Complete View Click'.split(z),
        u = 'identify login logout setAttribute'.split(z)
      if (!r || !r.__v) {
        for (w[b] = r = { __queue: [], __v: '6.0', user: {} }, i = 0; i < l.length; i++) o(r, [l[i]])
        for (i = 0; i < a.length; i++) {
          for (r[a[i]] = {}, s = 0; s < c.length; s++) o(r[a[i]], [a[i], c[s]])
          for (s = 0; s < p.length; s++) o(r[a[i]], [a[i], 'on' + p[s]])
        }
        for (i = 0; i < u.length; i++) o(r.user, ['user', u[i]])
        setTimeout(function() {
          var f = e.createElement('script'), d = e.getElementById('_webengage_script_tag')
          f.type = 'text/javascript', f.async = !0, f.src = ('https:' == e.location.protocol ? 'https://widgets.in.webengage.com' : 'http://widgets.in.webengage.com') + '/js/webengage-min-v-6.0.js', d.parentNode.insertBefore(f, d)
        })
      }
    }(window, document, 'webengage')
    webengage.init('in~~c2ab3735')
  </script>

</head>

<body>
<!-- Use static rendering with Expo Router to support running without JavaScript. -->
<noscript>
  You need to enable JavaScript to run this app.
</noscript>
<!-- The root element for your Expo app. -->
<div id="root"></div>
</body>
</html>
