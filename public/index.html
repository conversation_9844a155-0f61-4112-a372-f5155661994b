<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta content="IE=edge" httpEquiv="X-UA-Compatible" />
    <meta content="width=device-width, initial-scale=1, shrink-to-fit=no" name="viewport" />
    <title>Matiks - Mental Math Duels & Puzzle Battles | Train Your Brain</title>

    <meta content="Matiks - Mental Math Duels & Puzzle Battles | Train Your Brain" name="title" />
    <meta
        content="Challenge players worldwide in fast-paced math duels and puzzle battles on Matiks! Compete for global rankings, improve your speed & accuracy, and climb the leaderboard—just like chess, but with math!"
        name="description" />
    <meta
        content="Matiks, mental aptitude, gamification, mental math, educational games, speed and accuracy, brain games"
        name="keywords" />
    <meta content="index, follow" name="robots" />

    <!-- Open Graph Meta Tags -->
    <meta content="Matiks - Mental Math Duels & Puzzle Battles | Train Your Brain" property="og:title" />
    <meta
        content="Challenge players worldwide in fast-paced math duels and puzzle battles on Matiks! Compete for global rankings, improve your speed & accuracy, and climb the leaderboard—just like chess, but with math!"
        property="og:description" />
    <meta content="https://www.matiks.in" property="og:url" />
    <meta content="https://www.matiks.in/icon.png" property="og:image" />
    <meta content="website" property="og:type" />

    <meta
        content="Challenge players worldwide in fast-paced math duels and puzzle battles on Matiks! Compete for global rankings, improve your speed & accuracy, and climb the leaderboard—just like chess, but with math!"
        property="og:description" />
    <meta content="https://www.matiks.com" property="og:url" />
    <meta content="https://www.matiks.in/icon.png" property="og:image" />
    <meta content="website" property="og:type" />
    <meta content="origin" name="referrer">

    <!-- Twitter Card Meta Tags -->
    <meta content="summary_large_image" name="twitter:card" />
    <meta content="Matiks - Mental Math Duels & Puzzle Battles | Train Your Brain" name="twitter:title" />
    <meta
        content="Challenge players worldwide in fast-paced math duels and puzzle battles on Matiks! Compete for global rankings, improve your speed & accuracy, and climb the leaderboard—just like chess, but with math!"
        name="twitter:description" />
    <meta content="@matiks_play" name="twitter:site" />
    <meta content="https://www.matiks.in/icon.png" name="twitter:image" />
    <link href="favicon.ico" rel="shortcut icon" type="image/x-icon" />
    <link rel="manifest" href="/manifest.json">
    <link href="favicon.ico" rel="icon" type="image/x-icon" />
    <link href="katex/Katex.min.css" rel="stylesheet">
    <script defer src="katex/Katex.min.js"></script>
    <!-- The `react-native-web` recommended style reset: https://necolas.github.io/react-native-web/docs/setup/#root-element -->
    <style id="expo-reset">
        /* These styles make the body full-height */
        html,
        body {
            height: 100%;
        }

        /* These styles disable body scrolling if you are using <ScrollView> */
        body {
            overflow: hidden;
        }

        /* These styles make the root element full-height */
        #root {
            display: flex;
            height: 100%;
            flex: 1;
        }
    </style>
    <!-- CSP Meta Tag for Security -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' https://widgets.in.webengage.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https:; font-src 'self' https://fonts.gstatic.com data:; connect-src 'self' https://matiks.com https://dev.matiks.com https://widgets.in.webengage.com wss://matiks.com wss://dev.matiks.com; frame-ancestors 'self'; object-src 'none'; base-uri 'self'; form-action 'self'">

    <!-- External WebEngage script (CSP compliant) -->
    <script src="/js/webengage-init.js" async></script>

</head>

<body>
<!-- Use static rendering with Expo Router to support running without JavaScript. -->
<noscript>
    You need to enable JavaScript to run this app.
</noscript>
<!-- The root element for your Expo app. -->
<div id="root"></div>
</body>
</html>
