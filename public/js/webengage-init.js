/**
 * WebEngage Initialization Script
 * 
 * This script initializes WebEngage in a CSP-compliant way.
 * Moved from inline script to external file to comply with CSP.
 */

(function(window, document) {
  'use strict';
  
  // WebEngage configuration
  const WEBENGAGE_LICENSE_CODE = 'in~~c2ab3735';
  
  // Initialize WebEngage
  function initializeWebEngage() {
    var webengage;
    
    !function(w, e, b, n, g) {
      function o(e, t) {
        e[t[t.length - 1]] = function() {
          r.__queue.push([t.join('.'), arguments]);
        };
      }
      
      var i, s, r = w[b], z = ' ',
          l = 'init identify track screen onReady'.split(z),
          a = 'feedback survey notification'.split(z),
          c = 'options render clear abort'.split(z),
          u = 'Open Close Submit Complete View Click'.split(z),
          m = 'Shown Dismissed'.split(z);
      
      if (!r || !r.__v) {
        for (w[b] = r = { __queue: [], __v: 1.0 }, i = 0; i < l.length; i++) {
          o(r, [l[i]]);
        }
        
        for (i = 0; i < a.length; i++) {
          for (r[a[i]] = {}, s = 0; s < c.length; s++) {
            o(r[a[i]], [a[i], c[s]]);
          }
          
          for (s = 0; s < u.length; s++) {
            o(r[a[i]], [a[i], 'on' + u[s]]);
          }
          
          for (s = 0; s < m.length; s++) {
            o(r[a[i]], [a[i], 'on' + u[0] + m[s]]);
          }
        }
        
        for (i = 0; i < a.length; i++) {
          for (s = 0; s < u.length; s++) {
            o(r[a[i]], [a[i], 'on' + u[s]]);
          }
        }
        
        var f = ['track', 'screen', 'onReady'];
        for (i = 0; i < f.length; i++) {
          var k = f[i];
          for (s = 0; s < u.length; s++) {
            o(r, [k, 'on' + u[s]]);
          }
        }
        
        var v = ['Impression', 'Click'];
        for (i = 0; i < v.length; i++) {
          o(r, ['track', 'on' + v[i]]);
        }
        
        var p = ['', 'notification', 'survey', 'feedback'];
        for (i = 0; i < p.length; i++) {
          for (s = 0; s < u.length; s++) {
            o(r.user, ['user', p[i] + 'on' + u[s]]);
          }
        }
        
        var q = ['', 'notification', 'survey', 'feedback'];
        for (i = 0; i < q.length; i++) {
          for (s = 0; s < m.length; s++) {
            o(r.user, ['user', q[i] + 'on' + u[0] + m[s]]);
          }
        }
        
        var t = ['login', 'logout', 'setAttribute', 'getAttribute', 'removeAttribute', 'setEmail', 'setFirstName', 'setLastName', 'setPhone', 'setBirthDate', 'setGender', 'setCompany', 'setHashedEmail', 'setHashedPhone', 'setLocation'];
        for (i = 0; i < t.length; i++) {
          o(r.user, ['user', t[i]]);
        }
        
        setTimeout(function() {
          var f = e.createElement('script'),
              d = e.getElementById('_webengage_script_tag') || e.getElementsByTagName('script')[0];
          
          f.type = 'text/javascript';
          f.async = true;
          f.src = (e.location.protocol === 'https:' ? 'https://widgets.in.webengage.com' : 'http://widgets.in.webengage.com') + '/js/webengage-min-v-6.0.js';
          
          if (d && d.parentNode) {
            d.parentNode.insertBefore(f, d);
          } else {
            e.head.appendChild(f);
          }
        }, 0);
      }
    }(window, document, 'webengage');
    
    // Initialize with license code
    if (window.webengage) {
      window.webengage.init(WEBENGAGE_LICENSE_CODE);
    }
  }
  
  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeWebEngage);
  } else {
    initializeWebEngage();
  }
  
})(window, document);
