/**
 * CSP Violation Report Endpoint
 * 
 * This API endpoint receives and processes Content Security Policy violation reports
 * sent by browsers when CSP violations occur.
 */

export default function handler(req, res) {
  // Only accept POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const report = req.body;
    
    if (report && report['csp-report']) {
      const violation = report['csp-report'];
      
      // Extract useful information from the violation report
      const violationData = {
        timestamp: new Date().toISOString(),
        userAgent: req.headers['user-agent'],
        ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
        violation: {
          blockedURI: violation['blocked-uri'],
          violatedDirective: violation['violated-directive'],
          originalPolicy: violation['original-policy'],
          documentURI: violation['document-uri'],
          sourceFile: violation['source-file'],
          lineNumber: violation['line-number'],
          columnNumber: violation['column-number'],
          sample: violation['script-sample']
        }
      };
      
      // Log the violation (in production, you might want to send this to a monitoring service)
      console.warn('CSP Violation Report:', JSON.stringify(violationData, null, 2));
      
      // In production, you can send this to your monitoring/analytics service
      if (process.env.NODE_ENV === 'production') {
        // Example integrations:
        
        // 1. Send to Sentry
        // if (global.Sentry) {
        //   global.Sentry.captureMessage('CSP Violation', {
        //     level: 'warning',
        //     extra: violationData
        //   });
        // }
        
        // 2. Send to your analytics service
        // analytics.track('CSP_Violation', violationData);
        
        // 3. Send to your logging service
        // logger.warn('CSP Violation', violationData);
        
        // 4. Send to Bugsnag (since you're using it)
        if (global.Bugsnag) {
          global.Bugsnag.notify(new Error('CSP Violation'), {
            severity: 'warning',
            metaData: {
              csp: violationData
            }
          });
        }
      }
      
      // Store violation in database for analysis (optional)
      // await storeCSPViolation(violationData);
      
    } else {
      console.warn('Invalid CSP report format:', report);
    }
    
    // Always return 204 No Content for CSP reports
    res.status(204).end();
    
  } catch (error) {
    console.error('Error processing CSP violation report:', error);
    res.status(400).json({ error: 'Invalid report format' });
  }
}
