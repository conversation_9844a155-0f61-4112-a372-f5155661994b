<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebAssembly CSP Test</title>
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-eval'; style-src 'self' 'unsafe-inline';">
</head>
<body>
    <h1>WebAssembly CSP Test</h1>
    <div id="result">Testing...</div>
    
    <script>
        async function testWebAssembly() {
            const resultDiv = document.getElementById('result');
            
            try {
                // Simple WebAssembly module that exports an add function
                const wasmCode = new Uint8Array([
                    0x00, 0x61, 0x73, 0x6d, // WASM magic number
                    0x01, 0x00, 0x00, 0x00, // WASM version
                    0x01, 0x07, 0x01, 0x60, 0x02, 0x7f, 0x7f, 0x01, 0x7f, // Type section: (i32, i32) -> i32
                    0x03, 0x02, 0x01, 0x00, // Function section
                    0x07, 0x07, 0x01, 0x03, 0x61, 0x64, 0x64, 0x00, 0x00, // Export section: export "add" function
                    0x0a, 0x09, 0x01, 0x07, 0x00, 0x20, 0x00, 0x20, 0x01, 0x6a, 0x0b // Code section: add function
                ]);
                
                console.log('Compiling WebAssembly module...');
                const wasmModule = await WebAssembly.compile(wasmCode);
                
                console.log('Instantiating WebAssembly module...');
                const wasmInstance = await WebAssembly.instantiate(wasmModule);
                
                console.log('Testing WebAssembly function...');
                const result = wasmInstance.exports.add(5, 3);
                
                if (result === 8) {
                    resultDiv.innerHTML = '<span style="color: green;">✅ WebAssembly works with CSP! 5 + 3 = ' + result + '</span>';
                    console.log('✅ WebAssembly test passed!');
                } else {
                    resultDiv.innerHTML = '<span style="color: red;">❌ WebAssembly function returned unexpected result: ' + result + '</span>';
                }
                
            } catch (error) {
                console.error('WebAssembly test failed:', error);
                
                if (error.message.includes('unsafe-eval') || error.message.includes('Content Security Policy')) {
                    resultDiv.innerHTML = '<span style="color: red;">❌ CSP blocks WebAssembly: ' + error.message + '</span>';
                } else {
                    resultDiv.innerHTML = '<span style="color: red;">❌ WebAssembly error: ' + error.message + '</span>';
                }
            }
        }
        
        // Run test when page loads
        window.addEventListener('load', testWebAssembly);
    </script>
</body>
</html>